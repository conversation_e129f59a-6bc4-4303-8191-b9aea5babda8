<?php $__env->startSection('content'); ?>
    <!-- Header -->
    <header class="bg-blue-800 text-white p-4 shadow-md rounded mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold">Report Review</h1>
                <p class="text-blue-200">Report ID: <?php echo e($report->display_id); ?></p>
            </div>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('admin.reports.index')); ?>" class="bg-white text-blue-800 px-4 py-2 rounded shadow hover:bg-gray-100 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <button type="button" onclick="window.print()" class="bg-white text-blue-800 px-4 py-2 rounded shadow hover:bg-gray-100 flex items-center">
                    <i class="fas fa-print mr-2"></i> Print
                </button>
            </div>
        </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Report Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Report Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Report Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report ID</label>
                        <p class="text-gray-900 font-semibold"><?php echo e($report->display_id); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <span class="px-3 py-1 text-sm font-semibold rounded-full
                            <?php echo e($report->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                               ($report->status == 'resolved' ? 'bg-green-100 text-green-800' : 
                               ($report->status == 'review' ? 'bg-blue-100 text-blue-800' : 
                               ($report->status == 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')))); ?>">
                            <?php echo e(ucfirst($report->status)); ?>

                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <p class="text-gray-900"><?php echo e($report->category ? ucfirst(str_replace('_', ' ', $report->category)) : 'Not categorized'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Submission Date</label>
                        <p class="text-gray-900"><?php echo e($report->created_at->format('F d, Y \a\t g:i A')); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Incident Date</label>
                        <p class="text-gray-900"><?php echo e($report->incident_date ? $report->incident_date->format('F d, Y \a\t g:i A') : 'Not specified'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p class="text-gray-900"><?php echo e($report->location ?: 'Not specified'); ?></p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <div class="bg-gray-50 p-3 rounded border">
                        <p class="text-gray-900 whitespace-pre-wrap"><?php echo e($report->description ?: 'No description provided'); ?></p>
                    </div>
                </div>

                <?php if($report->unsafe_condition || $report->unsafe_act): ?>
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <?php if($report->unsafe_condition): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Unsafe Condition</label>
                        <p class="text-gray-900"><?php echo e($report->unsafe_condition); ?></p>
                        <?php if($report->other_unsafe_condition): ?>
                        <p class="text-gray-600 text-sm mt-1">Details: <?php echo e($report->other_unsafe_condition); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($report->unsafe_act): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Unsafe Act</label>
                        <p class="text-gray-900"><?php echo e($report->unsafe_act); ?></p>
                        <?php if($report->other_unsafe_act): ?>
                        <p class="text-gray-600 text-sm mt-1">Details: <?php echo e($report->other_unsafe_act); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php if($report->attachment): ?>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Attachment</label>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-paperclip text-gray-500"></i>
                        <a href="<?php echo e(asset('storage/' . $report->attachment)); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                            View Attachment
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Reporter Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Reporter Information</h2>
                <?php if($report->is_anonymous): ?>
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-4">
                        <div class="flex items-center">
                            <i class="fas fa-user-secret text-yellow-600 mr-2"></i>
                            <span class="text-yellow-800 font-medium">Anonymous Report</span>
                        </div>
                        <p class="text-yellow-700 text-sm mt-1">Reporter details are not available for anonymous reports.</p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <p class="text-gray-900"><?php echo e($report->user ? $report->user->name : 'Not available'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <p class="text-gray-900"><?php echo e($report->user ? $report->user->email : 'Not available'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
                            <p class="text-gray-900"><?php echo e($report->employee_id ?: 'Not provided'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <p class="text-gray-900"><?php echo e($report->phone ?: 'Not provided'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                            <p class="text-gray-900"><?php echo e($report->department ?: ($report->user && $report->user->department ? $report->user->department : 'Not specified')); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Handling Information -->
            <?php if($report->handlingDepartment || $report->handlingStaff || $report->deadline): ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Handling Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <?php if($report->handlingDepartment): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Assigned Department</label>
                        <p class="text-gray-900"><?php echo e($report->handlingDepartment ? $report->handlingDepartment->name : 'Not assigned'); ?></p>
                    </div>
                    <?php endif; ?>

                    <?php if($report->handlingStaff): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Assigned Staff</label>
                        <p class="text-gray-900"><?php echo e($report->handlingStaff ? $report->handlingStaff->name : 'Not assigned'); ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($report->deadline): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                        <p class="text-gray-900 <?php echo e($report->isOverdue() ? 'text-red-600 font-semibold' : ''); ?>">
                            <?php echo e($report->deadline->format('F d, Y')); ?>

                            <?php if($report->isOverdue()): ?>
                                <span class="text-red-600 text-sm">(Overdue)</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar - Actions and History -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 text-gray-800">Quick Actions</h3>
                <div class="space-y-3">
                    <?php if($report->status === 'pending'): ?>
                        <!-- Accept Button - Only for pending status -->
                        <form action="<?php echo e(route('admin.reports.accept', $report->id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center justify-center" onclick="return confirm('Are you sure you want to accept this report?')">
                                <i class="fas fa-check mr-2"></i>
                                Accept Report
                            </button>
                        </form>

                        <!-- Reject Button - Only for pending status -->
                        <button type="button" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 flex items-center justify-center" data-toggle="modal" data-target="#rejectModal">
                            <i class="fas fa-times mr-2"></i>
                            Reject Report
                        </button>
                    <?php endif; ?>

                    <?php if(in_array($report->status, ['pending', 'review', 'in_progress'])): ?>
                        <!-- Update Status Button - Available for non-resolved/non-rejected reports -->
                        <button type="button" class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center justify-center" data-toggle="modal" data-target="#updateStatusModal">
                            <i class="fas fa-edit mr-2"></i>
                            Update Status
                        </button>
                    <?php endif; ?>

                    <?php if($report->status === 'resolved'): ?>
                        <!-- Export/Archive Button - Only for resolved reports -->
                        <button type="button" class="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 flex items-center justify-center">
                            <i class="fas fa-download mr-2"></i>
                            Export Report
                        </button>
                    <?php endif; ?>

                    <?php if($report->status === 'rejected'): ?>
                        <!-- Status Info - For rejected reports -->
                        <div class="w-full bg-red-100 text-red-800 px-4 py-2 rounded flex items-center justify-center">
                            <i class="fas fa-times-circle mr-2"></i>
                            Report Rejected
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Assignment Information -->
            <?php if($report->handlingDepartment || $report->assignment_remark || $report->deadline): ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 text-gray-800">Assignment Details</h3>

                <?php if($report->handlingDepartment): ?>
                <div class="mb-4">
                    <span class="text-sm text-gray-500">Assigned to:</span>
                    <div class="text-base font-medium text-gray-800"><?php echo e($report->handlingDepartment->name); ?></div>
                </div>
                <?php endif; ?>

                <?php if($report->deadline): ?>
                <div class="mb-4">
                    <span class="text-sm text-gray-500">Deadline:</span>
                    <div class="text-base font-medium text-gray-800">
                        <?php echo e($report->deadline->format('M d, Y')); ?>

                        <?php if($report->isOverdue()): ?>
                            <span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">Overdue</span>
                        <?php elseif($report->daysUntilDeadline() !== null && $report->daysUntilDeadline() <= 3): ?>
                            <span class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Due Soon</span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($report->assignment_remark): ?>
                <div>
                    <span class="text-sm text-gray-500">Assignment Notes:</span>
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded mt-2">
                        <p class="text-sm text-gray-800"><?php echo e($report->assignment_remark); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Discussion Comments -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">Discussion Comments</h3>
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                            onclick="document.getElementById('main-comment-form').classList.toggle('hidden')">
                        <i class="fas fa-plus mr-1"></i>
                        Add Admin Comment
                    </button>
                </div>

                <!-- Main Comment Form -->
                <div id="main-comment-form" class="hidden mb-6 p-4 bg-gray-50 rounded-lg border">
                    <form method="POST" action="<?php echo e(route('admin.add-remarks')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="report_id" value="<?php echo e($report->id); ?>">

                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Admin Comment</label>
                            <textarea name="content"
                                      rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                                      placeholder="Add your administrative comment..."
                                      required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Attachment (optional)</label>
                            <input type="file"
                                   name="attachment"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt">
                            <p class="text-xs text-gray-500 mt-1">
                                Max 10MB. Allowed: JPG, PNG, PDF, DOC, XLS, TXT
                            </p>
                        </div>

                        <div class="flex items-center justify-end space-x-2">
                            <button type="button"
                                    class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                    onclick="document.getElementById('main-comment-form').classList.add('hidden')">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                <i class="fas fa-comment mr-1"></i>
                                Post Comment
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Threaded Comments Display -->
                <?php if(isset($threadedRemarks) && $threadedRemarks->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $threadedRemarks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (isset($component)) { $__componentOriginalcf77a05439254abdc287572920497a48 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcf77a05439254abdc287572920497a48 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-threaded-comment','data' => ['comment' => $comment,'report' => $report]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-threaded-comment'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcf77a05439254abdc287572920497a48)): ?>
<?php $attributes = $__attributesOriginalcf77a05439254abdc287572920497a48; ?>
<?php unset($__attributesOriginalcf77a05439254abdc287572920497a48); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcf77a05439254abdc287572920497a48)): ?>
<?php $component = $__componentOriginalcf77a05439254abdc287572920497a48; ?>
<?php unset($__componentOriginalcf77a05439254abdc287572920497a48); ?>
<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-comments text-gray-300 text-4xl mb-3"></i>
                        <p class="text-gray-500 text-sm">No discussion comments yet.</p>
                        <p class="text-gray-400 text-xs">Be the first to start the conversation!</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Activity (Warnings & Reminders) -->
            <?php if(($report->warnings && $report->warnings->count() > 0) || ($report->reminders && $report->reminders->count() > 0)): ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 text-gray-800">Recent Activity</h3>
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    <?php if($report->warnings && $report->warnings->count() > 0): ?>
                        <?php $__currentLoopData = $report->warnings->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warning): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-l-4 border-yellow-500 pl-3">
                            <p class="text-sm text-gray-600">Warning (<?php echo e(ucfirst($warning->type)); ?>) by <?php echo e($warning->suggestedBy ? $warning->suggestedBy->name : 'Unknown User'); ?></p>
                            <p class="text-sm text-gray-800"><?php echo e(Str::limit($warning->reason, 100)); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e($warning->created_at->diffForHumans()); ?></p>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>

                    <?php if($report->reminders && $report->reminders->count() > 0): ?>
                        <?php $__currentLoopData = $report->reminders->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reminder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-l-4 border-purple-500 pl-3">
                            <p class="text-sm text-gray-600">Reminder (<?php echo e(ucfirst($reminder->type)); ?>) by <?php echo e($reminder->sentBy ? $reminder->sentBy->name : 'Unknown User'); ?></p>
                            <p class="text-sm text-gray-800"><?php echo e(Str::limit($reminder->message, 100)); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e($reminder->created_at->diffForHumans()); ?></p>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Detailed History Sections -->

    <?php if($report->warnings && $report->warnings->count() > 0): ?>
    <div class="mt-6 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Warnings History</h2>
        <div class="space-y-4">
            <?php $__currentLoopData = $report->warnings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warning): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="border border-yellow-200 rounded p-4 bg-yellow-50">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <p class="font-semibold text-gray-800"><?php echo e($warning->suggestedBy ? $warning->suggestedBy->name : 'Unknown User'); ?></p>
                        <p class="text-sm text-gray-600"><?php echo e($warning->created_at->format('F d, Y \a\t g:i A')); ?></p>
                        <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-yellow-200 text-yellow-800 mt-1">
                            <?php echo e(ucfirst($warning->type)); ?> Warning
                        </span>
                    </div>
                </div>
                <div class="mt-2">
                    <p class="text-sm font-medium text-gray-700">Reason:</p>
                    <p class="text-gray-900"><?php echo e($warning->reason); ?></p>
                </div>
                <div class="mt-2">
                    <p class="text-sm font-medium text-gray-700">Suggested Action:</p>
                    <p class="text-gray-900"><?php echo e($warning->suggested_action); ?></p>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if($report->reminders && $report->reminders->count() > 0): ?>
    <div class="mt-6 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Reminders History</h2>
        <div class="space-y-4">
            <?php $__currentLoopData = $report->reminders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reminder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="border border-blue-200 rounded p-4 bg-blue-50">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <p class="font-semibold text-gray-800"><?php echo e($reminder->sentBy ? $reminder->sentBy->name : 'Unknown User'); ?></p>
                        <p class="text-sm text-gray-600"><?php echo e($reminder->created_at->format('F d, Y \a\t g:i A')); ?></p>
                        <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-blue-200 text-blue-800 mt-1">
                            <?php echo e(ucfirst($reminder->type)); ?> Reminder
                        </span>
                    </div>
                </div>
                <?php if($reminder->message): ?>
                <div class="mt-2">
                    <p class="text-gray-900"><?php echo e($reminder->message); ?></p>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Report Status</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.reports.update-status', $report->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="pending" <?php echo e($report->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="review" <?php echo e($report->status == 'review' ? 'selected' : ''); ?>>Under Review</option>
                                <option value="in_progress" <?php echo e($report->status == 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                                <option value="resolved" <?php echo e($report->status == 'resolved' ? 'selected' : ''); ?>>Resolved</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-control" id="category" name="category" required>
                                <option value="unsafe_act" <?php echo e($report->category == 'unsafe_act' ? 'selected' : ''); ?>>Unsafe Act</option>
                                <option value="unsafe_condition" <?php echo e($report->category == 'unsafe_condition' ? 'selected' : ''); ?>>Unsafe Condition</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php if($report->status === 'pending'): ?>
    <!-- Reject Modal - Only for pending reports -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">Reject Report <?php echo e($report->display_id); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.reports.reject', $report->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Rejection Reason</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4" required placeholder="Please provide a detailed reason for rejecting this report..."></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong>Warning:</strong> Rejecting this report will prevent further processing. This action should only be taken for invalid or duplicate reports.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Report</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ucua-fyp\resources\views/admin/reports/show.blade.php ENDPATH**/ ?>